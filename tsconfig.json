{"include": ["next-env.d.ts", ".next/types/**/*.ts", "declarations.d.ts", "src"], "exclude": ["node_modules"], "compilerOptions": {"baseUrl": ".", "declaration": true, "downlevelIteration": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "jsx": "preserve", "lib": ["dom", "esnext"], "target": "esnext", "module": "esnext", "moduleResolution": "node", "allowJs": true, "resolveJsonModule": true, "isolatedModules": true, "incremental": true, "noEmit": true, "noFallthroughCasesInSwitch": true, "noImplicitReturns": true, "skipLibCheck": true, "sourceMap": true, "strict": true, "plugins": [{"name": "next"}]}}