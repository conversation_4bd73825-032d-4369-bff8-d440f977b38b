{"name": "vaibhavkhatingfolio2025", "description": "My personal website and portfolio.", "author": "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "license": "MIT", "scripts": {"dev": "next dev --turbo", "build": "next build", "lint:prettier": "npm run prettier '**/*.{cjs,mjs,ts,tsx,json,md,yml,css}'", "lint:stylelint": "stylelint '**/*.css' --fix", "lint:tsc": "tsc --project tsconfig.json"}, "dependencies": {"bright": "^0.8.5", "clsx": "^2.1.0", "date-fns": "^3.6.0", "fast-base64": "^0.1.8", "fast-xml-parser": "^4.3.6", "framer-motion": "^11.0.20", "hast-util-to-html": "^9.0.0", "hastscript": "^9.0.0", "html-entities": "^2.5.2", "next": "^15.0.0", "next-themes": "^0.3.0", "react": "^19.0.0", "react-dom": "^19.0.0", "swr": "^2.2.5"}, "devDependencies": {"@tailwindcss/typography": "^0.5.10", "@types/node": "^20.11.30", "@types/react": "^18.2.69", "@types/react-dom": "^18.2.22", "autoprefixer": "^10.4.19", "eslint": "^8.57.0", "execa": "^8.0.1", "postcss": "^8.4.38", "postcss-nesting": "^12.1.0", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.5.12", "stylelint": "^15.11.0", "tailwindcss": "^3.4.1", "typescript": "^5.4.3", "write-json-file": "^5.0.0"}}