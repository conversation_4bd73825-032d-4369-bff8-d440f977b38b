@tailwind base;
@tailwind components;
@tailwind utilities;

@keyframes scroll-gradient {
  from {
    transform: translateY(0%);
  }

  to {
    transform: translateY(-87.5%);
  }
}

@keyframes wavy {
  from {
    transform: translateY(-3%);
  }

  to {
    transform: translateY(3%);
  }
}

@keyframes skeleton {
  from {
    background-position: 200% 0%;
  }

  to {
    background-position: -200% 0%;
  }
}

@keyframes aura-rainbow {
  from {
    transform: translateY(var(--aura-rainbow-offset)) rotate(0deg);
  }

  to {
    transform: translateY(var(--aura-rainbow-offset)) rotate(-360deg);
  }
}

/* stylelint-disable selector-id-pattern */
@layer base {
  html,
  body {
    @apply min-h-screen max-w-full overflow-x-hidden;
  }

  body {
    @apply flex flex-col bg-white font-sans selection:bg-gray-500/30 dark:bg-gray-950 dark:selection:bg-gray-400/40;

    -webkit-tap-highlight-color: transparent;

    > * {
      @apply w-full;
    }
  }

  body.grabbing {
    @apply cursor-grabbing;

    * {
      @apply pointer-events-none select-none;
    }
  }

  a,
  option,
  label,
  *[role="button"],
  button:not([disabled]),
  input:not([disabled]),
  textarea:not([disabled]),
  select:not([disabled]) {
    @apply touch-manipulation;
  }
}
/* stylelint-enable selector-id-pattern */

@layer utilities {
  .focusable {
    @apply box-decoration-clone focus:!decoration-transparent focus:outline-none focus:ring focus:ring-gray-500/40 dark:focus:ring-gray-400/40;
  }

  .focusable-within {
    @apply box-decoration-clone focus-within:!decoration-transparent focus-within:outline-none focus-within:ring focus-within:ring-gray-500/40 dark:focus-within:ring-gray-400/40;
  }

  .highlight::after {
    @apply pointer-events-none absolute inset-0 z-10;

    content: "";
    border-radius: inherit;
    box-shadow: inset 0 0 0 1px rgb(0 0 0 / 6%);
  }

  .highlight-invert::after {
    @apply pointer-events-none absolute inset-0 z-10;

    content: "";
    border-radius: inherit;
    box-shadow: inset 0 0 0 1px rgb(255 255 255 / 8%);
  }

  .polaroid-highlight::after {
    @apply pointer-events-none absolute inset-0 z-10 shadow-polaroid;

    content: "";
    border-radius: inherit;
  }

  .link {
    @apply focusable rounded-sm font-medium underline decoration-gray-600/20 decoration-2 underline-offset-2 transition selection:decoration-gray-600/20 hover:decoration-gray-600/40 focus:decoration-transparent dark:decoration-white/20 dark:selection:decoration-white/20 dark:hover:decoration-white/40;
  }

  .link-invert {
    @apply decoration-white/20 selection:decoration-white/20 hover:decoration-white/40 dark:decoration-gray-600/20 dark:selection:decoration-gray-600/20 dark:hover:decoration-gray-600/40;
  }

  .perspective {
    perspective: 1000px;
  }
}

@layer components {
  .content {
    @apply mx-auto max-w-screen-md px-5-safe;
  }

  .content-md {
    @apply lg:max-w-screen-md-8;
  }

  .content-lg {
    @apply lg:max-w-screen-md-12;
  }

  .skeleton {
    background: linear-gradient(
      -90deg,
      theme("colors.gray.100"),
      theme("colors.gray.200"),
      theme("colors.gray.200"),
      theme("colors.gray.100")
    );
    background-size: 400% 100%;
    animation: 6s linear infinite skeleton;

    .dark & {
      background-image: linear-gradient(
        -90deg,
        theme("colors.gray.700"),
        theme("colors.gray.800"),
        theme("colors.gray.800"),
        theme("colors.gray.700")
      );
    }
  }
}

:not(pre) > code {
  @apply relative whitespace-nowrap text-sm font-semibold;

  margin: 0 0.4em;

  &::before {
    @apply absolute z-negative bg-gray-100 dark:bg-gray-750;

    content: "";
    inset: -0.25em -0.4em -0.3em;
    border-radius: 0.4em;
  }
}

.scroll-gradient::before {
  @apply absolute w-full;

  content: "";
  height: 800%;
  background: linear-gradient(
    0deg,
    theme("colors.violet.500"),
    theme("colors.blue.500"),
    theme("colors.cyan.500"),
    theme("colors.emerald.500"),
    theme("colors.yellow.500"),
    theme("colors.orange.500"),
    theme("colors.rose.500"),
    theme("colors.violet.500"),
    theme("colors.blue.500")
  );
  animation: 20s linear infinite alternate both scroll-gradient;
}

.aura {
  mask-image: radial-gradient(farthest-side at center -100%, #000, transparent);
}

.aura-rays {
  /* mask-image: linear-gradient(
      50deg,
      transparent 32%,
      #000 36%,
      transparent 38%,
      rgb(0 0 0 / 75%) 43%,
      transparent 47%,
      transparent 51%,
      rgb(0 0 0 / 50%) 54%,
      transparent 56%,
      #000 62%,
      rgb(0 0 0 / 35%) 65%,
      rgb(0 0 0 / 75%) 66%,
      transparent 71%
    ),
    linear-gradient(0deg, transparent 20%, rgb(0 0 0 / 60%)); */
  mask-position:
    60% 0%,
    0% 0%;
  mask-size:
    300% auto,
    auto;

  @media screen(sm) {
    mask-size:
      200% auto,
      auto;
  }

  @media screen(md) {
    mask-position:
      50% 0%,
      0% 0%;
    mask-size:
      110% auto,
      auto;
  }
}

.aura-rainbow {
  background: conic-gradient(
    theme("colors.red.500"),
    theme("colors.pink.500"),
    theme("colors.violet.500"),
    theme("colors.blue.500"),
    theme("colors.sky.500"),
    theme("colors.teal.500"),
    theme("colors.lime.500"),
    theme("colors.amber.500"),
    theme("colors.red.500")
  );
  animation: 8s linear infinite aura-rainbow;

  .dark & {
    background: conic-gradient(
      theme("colors.red.300"),
      theme("colors.pink.300"),
      theme("colors.violet.300"),
      theme("colors.blue.300"),
      theme("colors.sky.300"),
      theme("colors.teal.300"),
      theme("colors.lime.300"),
      theme("colors.amber.300"),
      theme("colors.red.300")
    );
  }
}

.portrait {
  @apply highlight relative inline-block overflow-hidden rounded-full bg-white ring-offset-2;

  box-shadow:
    0 0 0 2px theme("colors.white"),
    0 0 1px 2px rgb(0 0 0 / 8%),
    0 1px 4px 2px rgb(0 0 0 / 8%),
    0 2px 8px 2px rgb(0 0 0 / 6%);

  & > span {
    @apply !absolute !inset-0 !h-full !w-full;
  }
}

.counter {
  mask-image: linear-gradient(
    0deg,
    transparent 0%,
    black 25%,
    black 75%,
    transparent 100%
  );
}

.delightful {
  margin-right: 0.15em;
}

.delightful span[data-character] {
  @apply relative mx-[-0.495em] inline-block px-[0.5em];

  animation: 0.8s ease-in-out calc(var(--character) * -0.1s) infinite alternate
    both wavy;
}

.wave {
  @apply relative translate-y-px;

  height: 4px;
  background: url("data:image/svg+xml,%3Csvg width='20' height='4' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0 1c3 0 3 2 5 2 3 0 3-2 5-2 3 0 3 2 5 2 3 0 3-2 5-2' stroke='%23e4e4e7'/%3E%3C/svg%3E");

  .dark & {
    background: url("data:image/svg+xml,%3Csvg width='20' height='4' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0 1c3 0 3 2 5 2 3 0 3-2 5-2 3 0 3 2 5 2 3 0 3-2 5-2' stroke='%23333338'/%3E%3C/svg%3E");
  }

  &::before,
  &::after {
    @apply absolute h-full w-16 max-w-full from-white dark:from-gray-900;

    content: "";
  }

  &::before {
    @apply left-0 bg-gradient-to-r;
  }

  &::after {
    @apply right-0 bg-gradient-to-l;
  }
}

.browser-header,
.code-header {
  grid-template-columns: 1fr minmax(0, 2fr) minmax(0, 1fr);
}

.code .bright {
  margin: 0 !important;

  pre {
    --line-number-color: rgb(255 255 255 / 30%);

    @apply !overflow-hidden !bg-transparent selection:!bg-white/10;

    padding: 0.5em 0 !important;
  }
}

.phone {
  aspect-ratio: 425 / 862;
  border-radius: 15.294% / 7.541%;
}

.phone-left .phone-screen-container {
  inset: 2.7% 5.4% 3.1% 6.35%;
}

.phone-right .phone-screen-container {
  inset: 2.7% 6.35% 3.1% 5.4%;
}

.phone-screen {
  clip-path: url("#phone-mask");
}

.headset {
  aspect-ratio: 960 / 540;
}

.headset-screen {
  clip-path: url("#headset-mask");
}

.book {
  aspect-ratio: 1214 / 1702;
}

.book-cover {
  border-radius: 4.615% / 3.297%;
}

.book-back-cover {
  inset: 5% 0 -5%;
  border-radius: 4.615%/3.297%;
}

.book-cover::before,
.book-back-cover::before,
.book-back-cover::after {
  @apply pointer-events-none absolute inset-0 z-10 rounded-[inherit];

  content: "";
}

.book-cover::before,
.book-back-cover::before {
  background: linear-gradient(
    90deg,
    rgb(0 0 0 / 12%) 0.5%,
    rgb(255 255 255 / 20%) 1.5%,
    rgb(255 255 255 / 10%) 2.5%,
    rgb(0 0 0 / 5%) 3.5%,
    rgb(255 255 255 / 14%) 5.5%,
    transparent 7%,
    transparent 96%,
    rgb(255 255 255 / 14%) 100%
  );
}

.book-back-cover::after {
  background: linear-gradient(
    180deg,
    transparent 94%,
    rgb(0 0 0 / 20%) 96%,
    transparent 100%
  );
}

.book-pages {
  inset: 3% 0 -3% 3%;
  border-radius: 4.615% / 3.297%;
  border-bottom-left-radius: 3.077% 2.198%;
  background-color: #fff;
  background-image: linear-gradient(
      180deg,
      rgb(0 0 0 / 20%) 96%,
      transparent 100%
    ),
    repeating-linear-gradient(
      transparent,
      transparent 1px,
      rgb(0 0 0 / 12%) 1px,
      rgb(0 0 0 / 12%) 2px
    );
}

.liveblocks-inbox::before {
  animation-delay: -4s;
}

.elements-music::before {
  animation-delay: -4s;
}

.transform-liveblocks-thread {
  transform: rotateX(26deg) rotateY(14deg) rotateZ(-10deg) translateX(2%);
}

.transform-liveblocks-inbox {
  transform: rotateX(14deg) rotateY(6deg) rotateZ(-4deg) translateX(-2%);
}

.transform-liveblocks-code {
  transform: rotateX(16deg) rotateY(18deg) rotateZ(-6deg) translateX(2%);
}

.transform-liveblocks-devtools {
  transform: rotateX(16deg) rotateY(-20deg) rotateZ(8deg) translateX(-2%);
}

.transform-liveblocks-io-marketing {
  transform: rotateX(18deg) rotateY(-16deg) rotateZ(8deg) translateX(-2%);
}

.transform-liveblocks-io-product {
  transform: rotateX(26deg) rotateY(22deg) rotateZ(-12deg) translateX(2%);
}

.transform-framer-com {
  transform: rotateX(16deg) rotateY(-20deg) rotateZ(8deg) translateX(-2%);
}

.transform-motion-video {
  transform: rotateX(18deg) rotateY(-20deg) rotateZ(8deg) translateX(-2%);
}

.transform-motion-static {
  transform: rotateX(12deg) rotateY(16deg) rotateZ(-4deg) translateX(2%);
}

.transform-elements {
  transform: rotateX(6deg) rotateY(-12deg) rotateZ(4deg) translateX(-2%);
}

.transform-master-phone {
  transform: rotate(-8deg);
}

.transform-master-headset {
  transform: rotate(-6deg);
}

.transform-master-thesis {
  transform: rotate(6deg);
}
