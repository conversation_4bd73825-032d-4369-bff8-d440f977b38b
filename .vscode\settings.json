{"css.validate": false, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.fixAll.stylelint": "explicit"}, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true, "eslint.validate": ["typescript", "typescriptreact"], "files.exclude": {"node_modules": true}, "prettier.ignorePath": ".giti<PERSON>re", "stylelint.enable": true, "[typescriptreact]": {"editor.defaultFormatter": "vscode.typescript-language-features"}}